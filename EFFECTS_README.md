# Effect Engine Overview - FachwerkwandApp

## System Architecture

Your effect engine is built around a **dual-deck DJ-style system** with sophisticated group management and real-time parameter control. The core components are:

1. **GroupEffectsEngine** (src/FachwerkwandApp.js:556) - Analyzes SVG structure and manages group-based effects
2. **useEffectEngine Hook** (src/FachwerkwandApp.js:1391) - Handles real-time animation rendering and crossfading
3. **Parameter Control System** (src/FachwerkwandApp.js:3012) - Provides live parameter adjustment interface

## Available Effect Types

### Single Element Effects
The system includes **15 core effect types** with full parameter control:

#### Basic Effects
- **pulse** - Pulsing with configurable color, intensity, speed, pulse width, brightness
- **colorFade** - Smooth color transitions between two colors with easing control
- **rainbow** - Animated rainbow cycles with speed, intensity, saturation, lightness controls
- **strobe** - Strobe lighting with frequency, intensity, duty cycle parameters

#### Transform Effects  
- **scale** - Size scaling with factor and speed control
- **rotate** - Rotation with degrees, speed, direction options
- **slide** - Position movement with X/Y offsets and easing
- **shake** - Random shake movement with intensity control

#### Atmospheric Effects
- **breathe** - Organic breathing opacity with rate, depth, asymmetry
- **opacity** - Opacity transitions between values
- **flicker** - Random flickering with intensity control

#### Architectural Effects (Fachwerk-specific)
- **windowGlow** - Window-specific glow effects with inner/outer glow, spread radius
- **warmWindowLight** - Warm candlelight simulation with flicker
- **shadowCast** - Dynamic shadow casting with length, angle, rotation
- **architecturalGlow** - Element-type-aware coloring (balken/gefach/fenster)
- **mirrorGlow** - Symmetrical left/right coloring effects
- **lightningFlash** - Lightning flash effects
- **rhythmicPulse** - Two-color rhythmic pulsing

## Group Effect Patterns

### Available Patterns (src/FachwerkwandApp.js:704)
The system provides **14 different group patterns**:

#### Basic Patterns
- **simultaneous** - All elements activate at once
- **sequence** - Sequential activation one after another  
- **alternating** - Every other element (A-B-A-B pattern)
- **wave** - Wave propagation from left to right
- **spiral** - Spiral activation from center outward
- **random** - Random element activation
- **pairs** - Activation in pairs
- **mirror** - Mirrored activation pattern

#### Advanced Patterns
- **sequentialChase** - Sequential with previous element stopping (chase effect)
- **alternatingChase** - Alternating with chase behavior
- **waveChase** - Wave pattern with chase stopping

#### Architectural Patterns (Fachwerk-specific)
- **stockwerkWave** - Floor-by-floor wave (bottom to top)
- **alternatingStockwerk** - Alternating floors
- **fensterReihe** - Window row pattern (left to right)
- **architekturalWave** - Structure → Surfaces → Windows progression

## Pre-built Group Effects Library

### Available Presets (src/FachwerkwandApp.js:1216)
- **🔥 Balken Glühen** - Intense beam pulsing (simultaneous pattern)
- **🌑 Balken Schattenwurf** - Dynamic shadow casting (sequence pattern)  
- **🌈 Gefache Regenbogen** - Lively rainbow on surfaces (alternating pattern)
- **✨ Fenster Glühen** - Warm window glow (simultaneous pattern)
- **🌈 Rainbow Chase ∞** - Endless rainbow chase (sequentialChase pattern)
- **🪟 Window Chase ∞** - Endless window chase (sequentialChase pattern)

## User Modification Capabilities

### Real-time Parameter Control
Users can modify effects through multiple interfaces:

1. **Parameter Sliders** (src/FachwerkwandApp.js:3141) - Live adjustment of all effect parameters
2. **Preset System** - Save/load custom parameter combinations per effect type
3. **Effect Parameter Panel** (src/FachwerkwandApp.js:3228) - Advanced parameter editing with presets

### Group Creation & Customization
- **Multi-Element Selection** - Create custom groups by selecting multiple elements
- **Pattern Selection** - Choose from 14+ different timing patterns
- **Custom Group Effects** - Combine any effect type with any pattern
- **Beat Sync Options** - Sync to beat, bar, or custom timing
- **Quick Presets** - Ready-made effect combinations for rapid deployment

### Live Control Features
- **Dual Deck System** - A/B deck with crossfading (src/FachwerkwandApp.js:1391)
- **BPM Sync** - Beat-synchronized effects with quantization
- **Preview Mode** - Test effects before adding to sequences
- **Real-time Updates** - Modify running effect parameters live
- **Speed Control** - Playback speed adjustment per deck
- **Volume/Intensity Control** - Master and per-deck volume control

## Advanced Features

### Crossfading & Mixing
- Smooth crossfading between deck A and B effects
- Element-specific crossfade values
- Layer ordering control

### Beat Engine Integration  
- BPM detection and sync
- Quantization options (beat, bar, custom)
- Beat-triggered effect starts

### Performance Optimization
- Real-time FPS monitoring (src/FachwerkwandApp.js:1395)
- Animation frame management
- Efficient SVG manipulation

## Effect Parameter Details

### Parameter Types
Each effect type supports different parameter types for user modification:

- **Color Picker** - RGB color selection with hex input
- **Range Slider** - Numeric values with min/max constraints and step size
- **Select Dropdown** - Predefined options (e.g., easing types, directions)
- **Boolean Toggle** - On/off switches for features

### Example Parameter Sets

#### Pulse Effect Parameters
```javascript
{
  color: '#ff6b6b',           // Color picker
  intensity: 0.8,             // Range: 0.1 - 2.0
  speed: 1.5,                 // Range: 0.1 - 5.0
  pulseWidth: 0.5,            // Range: 0.1 - 1.0
  brightness: 1.0             // Range: 0.2 - 3.0
}
```

#### Rainbow Effect Parameters
```javascript
{
  speed: 1.0,                 // Range: 0.1 - 5.0
  intensity: 0.8,             // Range: 0.1 - 2.0
  cycles: 2,                  // Range: 1 - 10
  saturation: 100,            // Range: 20 - 100
  lightness: 50               // Range: 20 - 80
}
```

#### Window Glow Parameters
```javascript
{
  glowColor: '#FFE4B5',       // Color picker
  intensity: 0.9,             // Range: 0.1 - 2.0
  spreadRadius: 8,            // Range: 2 - 30
  innerGlow: true,            // Boolean toggle
  pulseRate: 1.0              // Range: 0.1 - 3.0
}
```

## Group Structure Analysis

The system automatically analyzes SVG content to identify architectural elements:

### Detected Groups
- **balken** - Structural beams and timber elements
- **gefach** - Wall surfaces between timber frames
- **fenster** - Window elements
- **all** - All elements combined
- **horizontal** - Elements wider than tall
- **vertical** - Elements taller than wide

### Group Detection Logic
Elements are automatically categorized based on:
- Element ID patterns (containing "balken", "gefach", "fenster")
- Geometric properties (aspect ratio for horizontal/vertical)
- Spatial relationships

## Technical Implementation

### Animation Loop
The effect engine uses `requestAnimationFrame` for smooth 60fps animation with:
- Progress calculation based on elapsed time
- Easing function application
- Color interpolation and transformation matrix updates
- Cross-deck mixing and volume control

### Beat Synchronization
Effects can be synchronized to music with:
- Automatic BPM detection
- Manual BPM input
- Quantization to musical divisions (beat, bar, custom)
- Phase-locked effect triggering

### Memory Management
The system includes performance monitoring:
- Active animation counting
- FPS measurement
- Memory usage tracking
- Automatic cleanup of completed animations

---

The system is designed for **live performance** with DJ-style controls, making it ideal for real-time light shows synchronized to music on architectural visualizations.